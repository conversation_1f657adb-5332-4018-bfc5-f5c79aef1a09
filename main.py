from openai import OpenAI
import json

# Connect to LM Studio local API
client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

system_msg = {"role": "system", "content": "You are a helpful assistant."}

user_input = input("User: ")
user_msg = {"role": "user", "content": user_input}

resp = client.chat.completions.create(
    model="qwen-4b",
    messages=[system_msg, user_msg],
    temperature=0
)

msg = resp.choices[0].message
print("Commander Output:", msg)