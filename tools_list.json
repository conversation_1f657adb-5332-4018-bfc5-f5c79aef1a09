[{"type": "function", "function": {"name": "add_numbers", "description": "Add two numbers together", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "subtract_numbers", "description": "Subtract second number from first", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply_numbers", "description": "Multiply two numbers", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "divide_numbers", "description": "Divide first number by second", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "square_number", "description": "Square a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}, {"type": "function", "function": {"name": "cube_number", "description": "Cube a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}, {"type": "function", "function": {"name": "square_root", "description": "Find square root of a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}, {"type": "function", "function": {"name": "cube_root", "description": "Find cube root of a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}]