[{"type": "function", "function": {"name": "add_numbers", "description": "Add two numbers together", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "subtract_numbers", "description": "Subtract second number from first", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "multiply_numbers", "description": "Multiply two numbers", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "divide_numbers", "description": "Divide first number by second", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}}, {"type": "function", "function": {"name": "square_number", "description": "Square a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}, {"type": "function", "function": {"name": "cube_number", "description": "Cube a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}, {"type": "function", "function": {"name": "square_root", "description": "Find square root of a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}, {"type": "function", "function": {"name": "cube_root", "description": "Find cube root of a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}}, {"type": "function", "function": {"name": "reverse_text", "description": "Reverse the given text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "count_words", "description": "Count the number of words in the text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "count_characters", "description": "Count characters in text, optionally excluding spaces", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "include_spaces": {"type": "boolean", "default": true}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "to_uppercase", "description": "Convert text to uppercase", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "to_lowercase", "description": "Convert text to lowercase", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "to_title_case", "description": "Convert text to title case", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "capitalize_first", "description": "Capitalize only the first letter of the text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "remove_spaces", "description": "Remove all spaces from text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "remove_punctuation", "description": "Remove all punctuation from text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "extract_numbers", "description": "Extract all numbers from text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "extract_emails", "description": "Extract email addresses from text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "extract_urls", "description": "Extract URLs from text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "word_frequency", "description": "Count frequency of each word in text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "find_longest_word", "description": "Find the longest word in text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "find_shortest_word", "description": "Find the shortest word in text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "replace_text", "description": "Replace all occurrences of old_text with new_text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "old_text": {"type": "string"}, "new_text": {"type": "string"}}, "required": ["text", "old_text", "new_text"]}}}, {"type": "function", "function": {"name": "contains_word", "description": "Check if text contains a specific word (case-insensitive)", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "word": {"type": "string"}}, "required": ["text", "word"]}}}, {"type": "function", "function": {"name": "starts_with", "description": "Check if text starts with given prefix", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "prefix": {"type": "string"}}, "required": ["text", "prefix"]}}}, {"type": "function", "function": {"name": "ends_with", "description": "Check if text ends with given suffix", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "suffix": {"type": "string"}}, "required": ["text", "suffix"]}}}, {"type": "function", "function": {"name": "split_by_delimiter", "description": "Split text by a delimiter", "parameters": {"type": "object", "properties": {"text": {"type": "string"}, "delimiter": {"type": "string", "default": " "}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "join_with_delimiter", "description": "Join list of strings with a delimiter", "parameters": {"type": "object", "properties": {"text_list": {"type": "array", "items": {"type": "string"}}, "delimiter": {"type": "string", "default": " "}}, "required": ["text_list"]}}}, {"type": "function", "function": {"name": "remove_extra_spaces", "description": "Remove extra spaces and normalize whitespace", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "count_sentences", "description": "Count number of sentences in text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "count_paragraphs", "description": "Count number of paragraphs in text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "extract_hashtags", "description": "Extract hashtags from text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "extract_mentions", "description": "Extract @mentions from text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "is_palindrome", "description": "Check if text is a palindrome (ignoring spaces and case)", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "acronym_generator", "description": "Generate an acronym from the first letters of words", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}, {"type": "function", "function": {"name": "text_statistics", "description": "Get comprehensive statistics about the text", "parameters": {"type": "object", "properties": {"text": {"type": "string"}}, "required": ["text"]}}}]