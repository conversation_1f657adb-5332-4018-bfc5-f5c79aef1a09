"""
Text manipulation tools for the orchestrator
"""
import re
import string
from collections import Counter


def reverse_text(text):
    """Reverse the given text"""
    return text[::-1]


def count_words(text):
    """Count the number of words in the text"""
    return len(text.split())


def count_characters(text, include_spaces=True):
    """Count characters in text, optionally excluding spaces"""
    if include_spaces:
        return len(text)
    else:
        return len(text.replace(' ', ''))


def to_uppercase(text):
    """Convert text to uppercase"""
    return text.upper()


def to_lowercase(text):
    """Convert text to lowercase"""
    return text.lower()


def to_title_case(text):
    """Convert text to title case"""
    return text.title()


def capitalize_first(text):
    """Capitalize only the first letter of the text"""
    return text.capitalize()


def remove_spaces(text):
    """Remove all spaces from text"""
    return text.replace(' ', '')


def remove_punctuation(text):
    """Remove all punctuation from text"""
    return text.translate(str.maketrans('', '', string.punctuation))


def extract_numbers(text):
    """Extract all numbers from text"""
    numbers = re.findall(r'-?\d+\.?\d*', text)
    return [float(num) if '.' in num else int(num) for num in numbers]


def extract_emails(text):
    """Extract email addresses from text"""
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    return re.findall(email_pattern, text)


def extract_urls(text):
    """Extract URLs from text"""
    url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    return re.findall(url_pattern, text)


def word_frequency(text):
    """Count frequency of each word in text"""
    words = text.lower().split()
    # Remove punctuation from words
    words = [word.strip(string.punctuation) for word in words]
    return dict(Counter(words))


def find_longest_word(text):
    """Find the longest word in text"""
    words = text.split()
    # Remove punctuation for accurate length calculation
    words = [word.strip(string.punctuation) for word in words]
    return max(words, key=len) if words else ""


def find_shortest_word(text):
    """Find the shortest word in text"""
    words = text.split()
    # Remove punctuation and filter empty strings
    words = [word.strip(string.punctuation) for word in words if word.strip(string.punctuation)]
    return min(words, key=len) if words else ""


def replace_text(text, old_text, new_text):
    """Replace all occurrences of old_text with new_text"""
    return text.replace(old_text, new_text)


def contains_word(text, word):
    """Check if text contains a specific word (case-insensitive)"""
    return word.lower() in text.lower()


def starts_with(text, prefix):
    """Check if text starts with given prefix"""
    return text.startswith(prefix)


def ends_with(text, suffix):
    """Check if text ends with given suffix"""
    return text.endswith(suffix)


def split_by_delimiter(text, delimiter=" "):
    """Split text by a delimiter"""
    return text.split(delimiter)


def join_with_delimiter(text_list, delimiter=" "):
    """Join list of strings with a delimiter"""
    return delimiter.join(text_list)


def remove_extra_spaces(text):
    """Remove extra spaces and normalize whitespace"""
    return ' '.join(text.split())


def count_sentences(text):
    """Count number of sentences in text"""
    # Simple sentence counting based on sentence-ending punctuation
    sentences = re.split(r'[.!?]+', text)
    # Filter out empty strings
    sentences = [s.strip() for s in sentences if s.strip()]
    return len(sentences)


def count_paragraphs(text):
    """Count number of paragraphs in text"""
    paragraphs = text.split('\n\n')
    # Filter out empty paragraphs
    paragraphs = [p.strip() for p in paragraphs if p.strip()]
    return len(paragraphs)


def extract_hashtags(text):
    """Extract hashtags from text"""
    hashtag_pattern = r'#\w+'
    return re.findall(hashtag_pattern, text)


def extract_mentions(text):
    """Extract @mentions from text"""
    mention_pattern = r'@\w+'
    return re.findall(mention_pattern, text)


def is_palindrome(text):
    """Check if text is a palindrome (ignoring spaces and case)"""
    cleaned = re.sub(r'[^a-zA-Z0-9]', '', text.lower())
    return cleaned == cleaned[::-1]


def acronym_generator(text):
    """Generate an acronym from the first letters of words"""
    words = text.split()
    return ''.join(word[0].upper() for word in words if word)


def text_statistics(text):
    """Get comprehensive statistics about the text"""
    return {
        "character_count": len(text),
        "character_count_no_spaces": len(text.replace(' ', '')),
        "word_count": len(text.split()),
        "sentence_count": count_sentences(text),
        "paragraph_count": count_paragraphs(text),
        "longest_word": find_longest_word(text),
        "shortest_word": find_shortest_word(text),
        "average_word_length": sum(len(word.strip(string.punctuation)) for word in text.split()) / len(text.split()) if text.split() else 0
    }