from openai import OpenAI
import json
import importlib

client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

with open("tools_list.json", "r") as f:
    tools = json.load(f)

math_tools = importlib.import_module("tools.math_tools")

with open("system_prompt.txt","r") as f:
    system_prompt = f.read

user_query = input("User:")

messages = [
    {"role": "User", "content": user_query },
    {"role": "System", "content": system_prompt}

]
