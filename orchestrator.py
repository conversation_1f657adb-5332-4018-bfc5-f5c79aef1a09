from openai import OpenAI
import json
import importlib
import sys

client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

with open("tools_list.json", "r") as f:
    tools = json.load(f)

math_tools = importlib.import_module("tools.math_tools")

with open("system_prompt.txt","r") as f:
    system_prompt = f.read()

user_query = input("User:")

messages = [
    {"role": "user", "content": user_query},
    {"role": "system", "content": system_prompt}
]
def stream_print(text, end='\n'):
    """Print text with streaming effect"""
    for char in text:
        print(char, end='', flush=True)
        import time
        time.sleep(0.02)  # Small delay for streaming effect
    print(end, end='')

print("🤖 Thinking...")

# Ask model with streaming
response = client.chat.completions.create(
    model="qwen2.5-3b-instruct",
    messages=messages,
    tools=tools,
    tool_choice="auto",
    stream=True
)

# Collect streaming response
msg_content = ""
tool_calls = []
current_msg = None

for chunk in response:
    if chunk.choices[0].delta.content:
        content = chunk.choices[0].delta.content
        print(content, end='', flush=True)
        msg_content += content

    if chunk.choices[0].delta.tool_calls:
        tool_calls.extend(chunk.choices[0].delta.tool_calls)

    # Store the complete message when available
    if chunk.choices[0].finish_reason:
        current_msg = chunk.choices[0]

print()  # New line after streaming

# Handle tool calls if any
if tool_calls:
    print("\n🔧 Using tools...")

    # Add assistant message with tool calls to conversation
    messages.append({
        "role": "assistant",
        "content": msg_content,
        "tool_calls": [{"id": tc.id, "type": "function", "function": {"name": tc.function.name, "arguments": tc.function.arguments}} for tc in tool_calls if tc.function]
    })

    # Process each tool call
    for tool_call in tool_calls:
        if tool_call.function:
            name = tool_call.function.name
            args = json.loads(tool_call.function.arguments)

            print(f"  📊 Calling {name} with {args}")

            # Call the corresponding Python function from math_tools
            if hasattr(math_tools, name):
                func = getattr(math_tools, name)
                result = func(**args)
                print(f"  ✅ Result: {result}")
            else:
                result = f"Tool {name} not implemented."
                print(f"  ❌ {result}")

            # Append tool result
            messages.append({
                "role": "tool",
                "tool_call_id": tool_call.id,
                "content": str(result)
            })

    print("\n🤖 Generating final response...")

    # Ask model again with tool results (streaming)
    response = client.chat.completions.create(
        model="qwen2.5-3b-instruct",
        messages=messages,
        tools=tools,
        tool_choice="auto",
        stream=True
    )

    # Stream final response
    for chunk in response:
        if chunk.choices[0].delta.content:
            content = chunk.choices[0].delta.content
            print(content, end='', flush=True)

    print()  # New line after final response
else:
    # No tools needed, response is complete
    print(f"\n✨ Final answer: {msg_content}")