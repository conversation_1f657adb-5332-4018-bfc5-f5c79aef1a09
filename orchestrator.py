from openai import OpenAI

# Connect to LM Studio local API
client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

# === Stage 0: Define tools ===
TOOL_DOMAINS = {
    "math_tools": {
        "add_numbers": lambda a, b: a + b,
        "sub_numbers": lambda a, b: a - b,
        "multiply_numbers": lambda a, b: a * b
    },
    "text_tools": {
        "reverse_text": lambda text: text[::-1],
        "count_words": lambda text: len(text.split())
    }
}

# === Stage 1: Domain Selection ===
def select_domain(user_query):
    system_msg = {
        "role": "system",
        "content": f"You are a domain selector. Available domains: {', '.join(TOOL_DOMAINS.keys())}. "
                   f"Respond ONLY with the domain name in JSON: {{'domain': 'name'}}."
    }
    resp = client.chat.completions.create(
        model="qwen-4b",
        messages=[system_msg, {"role": "user", "content": user_query}]
    )
    return eval(resp.choices[0].message.content)["domain"]

# === Stage 2: Function Selection ===
def select_function(domain, user_query):
    funcs = list(TOOL_DOMAINS[domain].keys())
    system_msg = {
        "role": "system",
        "content": f"You are a function selector. Domain: {domain}. "
                   f"Available functions: {', '.join(funcs)}. "
                   f"Respond ONLY with JSON: {{'function': 'name'}}."
    }
    resp = client.chat.completions.create(
        model="qwen-4b",
        messages=[system_msg, {"role": "user", "content": user_query}]
    )
    return eval(resp.choices[0].message.content)["function"]

# === Stage 3: Parameter Collection ===
def get_parameters(func_name, user_query):
    system_msg = {
        "role": "system",
        "content": f"You are a parameter filler for function {func_name}. "
                   f"Return ONLY JSON of parameters: {{'a': 1, 'b': 2}}."
    }
    resp = client.chat.completions.create(
        model="qwen-4b",
        messages=[system_msg, {"role": "user", "content": user_query}]
    )
    return eval(resp.choices[0].message.content)

# === Orchestrator ===
def orchestrate(user_query):
    domain = select_domain(user_query)
    func_name = select_function(domain, user_query)
    params = get_parameters(func_name, user_query)
    result = TOOL_DOMAINS[domain][func_name](**params)
    return result

# === Run ===
if __name__ == "__main__":
    query = input("User: ")
    output = orchestrate(query)
    print("Result:", output)