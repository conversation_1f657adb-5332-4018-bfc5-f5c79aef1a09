from openai import OpenAI
import json
import importlib

client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

with open("tools_list.json", "r") as f:
    tools = json.load(f)

math_tools = importlib.import_module("tools.math_tools")

with open("system_prompt.txt","r") as f:
    system_prompt = f.read()

user_query = input("User:")

messages = [
    {"role": "user", "content": user_query},
    {"role": "system", "content": system_prompt}
]
def stream_print(text, end='\n'):
    """Print text with streaming effect"""
    for char in text:
        print(char, end='', flush=True)
        import time
        time.sleep(0.02)  # Small delay for streaming effect
    print(end, end='')

print("🤖 Thinking...")

# Ask model with streaming
response = client.chat.completions.create(
    model="qwen2.5-3b-instruct",
    messages=messages,
    tools=tools,
    tool_choice="auto",
    stream=True
)

# Collect streaming response
msg_content = ""
tool_calls_dict = {}
current_msg = None

for chunk in response:
    if chunk.choices[0].delta.content:
        content = chunk.choices[0].delta.content
        print(content, end='', flush=True)
        msg_content += content

    # Handle tool calls from streaming
    if chunk.choices[0].delta.tool_calls:
        for tc in chunk.choices[0].delta.tool_calls:
            if tc.index not in tool_calls_dict:
                tool_calls_dict[tc.index] = {
                    "id": tc.id,
                    "type": "function",
                    "function": {"name": "", "arguments": ""}
                }

            if tc.id:
                tool_calls_dict[tc.index]["id"] = tc.id
            if tc.function:
                if tc.function.name:
                    tool_calls_dict[tc.index]["function"]["name"] = tc.function.name
                if tc.function.arguments:
                    tool_calls_dict[tc.index]["function"]["arguments"] += tc.function.arguments

    # Store the complete message when available
    if chunk.choices[0].finish_reason:
        current_msg = chunk.choices[0]

# Convert tool calls dict to list
tool_calls = list(tool_calls_dict.values())

print()  # New line after streaming

# Handle tool calls if any
if tool_calls:
    print("\n🔧 Using tools...")

    # Add assistant message with tool calls to conversation
    messages.append({
        "role": "assistant",
        "content": msg_content,
        "tool_calls": tool_calls
    })

    # Process each tool call
    for tool_call in tool_calls:
        if tool_call.get("function") and tool_call["function"].get("name"):
            name = tool_call["function"]["name"]
            args_str = tool_call["function"]["arguments"]

            try:
                args = json.loads(args_str) if args_str else {}
            except json.JSONDecodeError:
                print(f"  ❌ Invalid JSON arguments for {name}: {args_str}")
                continue

            print(f"  📊 Calling {name} with {args}")

            # Call the corresponding Python function from math_tools
            try:
                if hasattr(math_tools, name):
                    func = getattr(math_tools, name)
                    result = func(**args)
                    print(f"  ✅ Result: {result}")
                else:
                    result = f"Tool {name} not implemented."
                    print(f"  ❌ {result}")
            except Exception as e:
                result = f"Error calling {name}: {str(e)}"
                print(f"  ❌ {result}")

            # Append tool result
            messages.append({
                "role": "tool",
                "tool_call_id": tool_call["id"],
                "content": str(result)
            })

    print("\n🤖 Generating final response...")

    # Ask model again with tool results (streaming)
    response = client.chat.completions.create(
        model="qwen2.5-3b-instruct",
        messages=messages,
        tools=tools,
        tool_choice="auto",
        stream=True
    )

    # Stream final response
    for chunk in response:
        if chunk.choices[0].delta.content:
            content = chunk.choices[0].delta.content
            print(content, end='', flush=True)

    print()  # New line after final response
else:
    # No tools needed, response is complete
    print(f"\n✨ Final answer: {msg_content}")