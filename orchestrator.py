from openai import OpenAI
import json
import importlib

client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

with open("tools_list.json", "r") as f:
    tools = json.load(f)

math_tools = importlib.import_module("tools.math_tools")

with open("system_prompt.txt","r") as f:
    system_prompt = f.read()

user_query = input("User:")

messages = [
    {"role": "user", "content": user_query},
    {"role": "system", "content": system_prompt}
]
# Ask model
response = client.chat.completions.create(
    model="qwen2.5-3b-instruct",
    messages=messages,
    tools=tools,
    tool_choice="auto"
)

msg = response.choices[0].message

while msg.tool_calls:
    for tool_call in msg.tool_calls:
        name = tool_call.function.name
        args = json.loads(tool_call.function.arguments)

        # Call the corresponding Python function from math_tools
        if hasattr(math_tools, name):
            func = getattr(math_tools, name)
            result = func(**args)
        else:
            result = f"Tool {name} not implemented."

        # Append tool result
        messages.append(msg)
        messages.append({
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": str(result)
        })

    # Ask model again with tool results
    response = client.chat.completions.create(
        model="qwen2.5-3b-instruct",
        messages=messages,
        tools=tools,
        tool_choice="auto"
    )
    msg = response.choices[0].message

# Final answer
print(msg["content"])