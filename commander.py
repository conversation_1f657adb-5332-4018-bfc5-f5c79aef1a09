from openai import OpenAI
import json
import tools.math_tools as math_tools

# Local Qwen through LM Studio
client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

# Tool registry: keep outside LLM
TOOL_REGISTRY = {
    "math.add": math_tools.add_numbers,
    "math.sub": math_tools.sub_numbers,
    "math.mul": math_tools.multiply_numbers,
    "math.div": math_tools.divide_numbers,
    "math.sqr": math_tools.square_number,   
    "math.cub": math_tools.cube_number,
    "math.sqrt": math_tools.square_root,
    "math.cbrt": math_tools.cube_root,
}

# System prompt — keep lean
system_msg = {
    "role": "system",
    "content": "You are the Commander. Decide the correct tool and args in JSON only."
}

def commander_llm(user_input):
    user_msg = {"role": "user", "content": user_input}

    resp = client.chat.completions.create(
        model="qwen-4b",
        messages=[system_msg, user_msg],
        temperature=0
    )

    raw = resp.choices[0].message.content
    return raw.strip()

def orchestrator(user_input):
    cmd_raw = commander_llm(user_input)
    try:
        decision = json.loads(cmd_raw)
        tool = decision.get("tool")
        args = decision.get("args", {})
    except Exception as e:
        return f"Commander output invalid: {cmd_raw} | Error: {e}"

    if tool not in TOOL_REGISTRY:
        return f"Unknown tool '{tool}'"
    
    return TOOL_REGISTRY[tool](**args)

if __name__ == "__main__":
    while True:
        text = input("\nUser: ")
        if text.lower() in ["exit", "quit"]:
            break
        print("Result:", orchestrator(text))